using System;
using System.Threading.Tasks;

namespace MartingaleTest
{
    /// <summary>
    /// Standalone Martingale implementation with correct mathematical formula
    /// </summary>
    public class OptimizedMartingale
    {
        private decimal _baseStake;
        private decimal _factor;
        private int _maxLevel;
        private int _currentLevel = 1;
        
        public void Initialize(decimal baseStake, decimal factor, int maxLevel)
        {
            _baseStake = baseStake;
            _factor = factor;
            _maxLevel = maxLevel;
            _currentLevel = 1;
        }
        
        public decimal CalculateCurrentStake()
        {
            // LÓGICA CORRIGIDA: Calcular stake sequencialmente
            decimal stake = _baseStake;
            for (int i = 1; i < _currentLevel; i++)
            {
                stake *= _factor;
            }
            return stake;
        }
        
        public void ProcessResult(bool isWin)
        {
            if (isWin)
            {
                _currentLevel = 1; // Reset to level 1 on win
            }
            else
            {
                if (_currentLevel < _maxLevel)
                {
                    _currentLevel++; // Increment level on loss
                }
                else
                {
                    _currentLevel = 1; // Reset if max level reached
                }
            }
        }
        
        public int GetCurrentLevel() => _currentLevel;
    }
    
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("🚀 Standalone Martingale Test - Optimized Implementation");
            Console.WriteLine("=".PadRight(60, '='));
            
            try
            {
                await TestMartingaleSequence();
                Console.WriteLine("\n✅ All tests passed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Test failed: {ex.Message}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
        
        static async Task TestMartingaleSequence()
        {
            Console.WriteLine("\n--- Test: Correct Martingale Sequence ---");
            
            var martingale = new OptimizedMartingale();
            decimal baseStake = 0.35m;
            decimal factor = 2.1m;
            int maxLevel = 8;
            
            martingale.Initialize(baseStake, factor, maxLevel);
            
            // Expected stakes for each level using correct formula: baseStake × factor^(level-1)
            decimal[] expectedStakes = {
                0.35m,          // Level 1: 0.35 × 2.1^0 = 0.35
                0.735m,         // Level 2: 0.35 × 2.1^1 = 0.735
                1.5435m,        // Level 3: 0.35 × 2.1^2 = 1.5435
                3.24135m,       // Level 4: 0.35 × 2.1^3 = 3.24135
                6.806835m,      // Level 5: 0.35 × 2.1^4 = 6.806835
                14.2943535m,    // Level 6: 0.35 × 2.1^5 = 14.2943535
                30.01814235m,   // Level 7: 0.35 × 2.1^6 = 30.01814235
                63.038298935m   // Level 8: 0.35 × 2.1^7 = 63.038298935
            };
            
            Console.WriteLine($"Configuration: BaseStake={baseStake}, Factor={factor}, MaxLevel={maxLevel}");
            Console.WriteLine("\nTesting stake calculation for each level:");
            
            // Test initial state
            var initialLevel = martingale.GetCurrentLevel();
            var initialStake = martingale.CalculateCurrentStake();
            Console.WriteLine($"\nInitial state - Level: {initialLevel}, Stake: {initialStake:F8}");
            
            if (initialLevel != 1)
                throw new Exception($"Expected initial level 1, got {initialLevel}");
            
            if (Math.Abs(initialStake - expectedStakes[0]) > 0.00000001m)
                throw new Exception($"Expected initial stake {expectedStakes[0]:F8}, got {initialStake:F8}");
            
            Console.WriteLine("✅ Initial state correct");
            
            // Test progression through losses
            for (int i = 1; i <= 7; i++)
            {
                Console.WriteLine($"\n--- Processing loss #{i} ---");
                
                // Process loss
                martingale.ProcessResult(false);
                
                var currentLevel = martingale.GetCurrentLevel();
                var currentStake = martingale.CalculateCurrentStake();
                var expectedStake = expectedStakes[currentLevel - 1];
                var difference = Math.Abs(currentStake - expectedStake);
                
                Console.WriteLine($"After loss #{i}:");
                Console.WriteLine($"  Level: {currentLevel}");
                Console.WriteLine($"  Stake calculated: {currentStake:F8}");
                Console.WriteLine($"  Stake expected:   {expectedStake:F8}");
                Console.WriteLine($"  Difference:       {difference:F8}");
                
                if (currentLevel != i + 1)
                    throw new Exception($"Expected level {i + 1}, got {currentLevel}");
                
                if (difference > 0.00000001m)
                    throw new Exception($"Stake calculation error at level {currentLevel}. Expected {expectedStake:F8}, got {currentStake:F8}");
                
                Console.WriteLine($"✅ Level {currentLevel} correct");
                await Task.Delay(100); // Small delay for readability
            }
            
            // Test win reset
            Console.WriteLine("\n--- Testing win reset ---");
            martingale.ProcessResult(true);
            
            var finalLevel = martingale.GetCurrentLevel();
            var finalStake = martingale.CalculateCurrentStake();
            
            Console.WriteLine($"After win - Level: {finalLevel}, Stake: {finalStake:F8}");
            
            if (finalLevel != 1)
                throw new Exception($"Expected level 1 after win, got {finalLevel}");
            
            if (Math.Abs(finalStake - baseStake) > 0.00000001m)
                throw new Exception($"Expected stake {baseStake:F8} after win, got {finalStake:F8}");
            
            Console.WriteLine("✅ Win reset correct");
            
            // Test max level reset
            Console.WriteLine("\n--- Testing max level reset ---");
            
            // Simulate losses to reach max level
            for (int i = 1; i <= maxLevel; i++)
            {
                martingale.ProcessResult(false);
            }
            
            var maxLevelResetLevel = martingale.GetCurrentLevel();
            var maxLevelResetStake = martingale.CalculateCurrentStake();
            
            Console.WriteLine($"After reaching max level - Level: {maxLevelResetLevel}, Stake: {maxLevelResetStake:F8}");
            
            if (maxLevelResetLevel != 1)
                throw new Exception($"Expected level 1 after max level reset, got {maxLevelResetLevel}");
            
            Console.WriteLine("✅ Max level reset correct");
            
            // Performance test
            Console.WriteLine("\n--- Performance test ---");
            var startTime = DateTime.UtcNow;
            
            for (int i = 0; i < 10000; i++)
            {
                martingale.ProcessResult(i % 2 == 0);
                martingale.CalculateCurrentStake();
            }
            
            var elapsed = DateTime.UtcNow - startTime;
            Console.WriteLine($"10,000 operations completed in {elapsed.TotalMilliseconds:F2}ms");
            
            if (elapsed.TotalSeconds > 0.5)
                throw new Exception("Performance test failed - operations took too long");
            
            Console.WriteLine("✅ Performance test passed");
            
            Console.WriteLine("\n🎉 All martingale tests completed successfully!");
            Console.WriteLine("\n📊 Summary:");
            Console.WriteLine("✅ Correct mathematical implementation (baseStake × factor^(level-1))");
            Console.WriteLine("✅ Proper level progression on losses");
            Console.WriteLine("✅ Correct reset to level 1 on wins");
            Console.WriteLine("✅ Max level handling");
            Console.WriteLine("✅ High performance (10k ops < 500ms)");
        }
    }
}
