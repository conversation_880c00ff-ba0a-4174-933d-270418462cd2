﻿using System;
using System.Threading.Tasks;

namespace TestRunner
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("🧪 Executando testes do sistema de Martingale...");
            Console.WriteLine("=".PadRight(50, '='));

            try
            {
                // Executar teste específico da sequência
                await TestMartingaleSequence();

                Console.WriteLine("\n✅ Todos os testes foram executados!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erro durante os testes: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\nPressione qualquer tecla para sair...");
            Console.ReadKey();
        }

        static async Task TestMartingaleSequence()
        {
            Console.WriteLine("\n--- Teste: Sequência de Martingale Corrigida ---");

            decimal baseStake = 0.35m;
            decimal factor = 2.1m;

            Console.WriteLine($"Configuração: BaseStake={baseStake}, Factor={factor}");
            Console.WriteLine("Sequência esperada (CORRIGIDA):");
            Console.WriteLine("Entrada 1 (nível 1): 0.35000000 (stake base)");
            Console.WriteLine("Após perda → Entrada 2 (nível 2): 0.73500000 (0.35 × 2.1^1)");
            Console.WriteLine("Após perda → Entrada 3 (nível 3): 1.54350000 (0.35 × 2.1^2)");
            Console.WriteLine("Após perda → Entrada 4 (nível 4): 3.24135000 (0.35 × 2.1^3)");
            Console.WriteLine("Após perda → Entrada 5 (nível 5): 6.80683500 (0.35 × 2.1^4)");
            Console.WriteLine("Após perda → Entrada 6 (nível 6): 14.29435350 (0.35 × 2.1^5)");
            Console.WriteLine();

            // Valores esperados exatos
            decimal[] expectedStakes = {
                0.35m,          // Nível 1: 0.35 × 2.1^0 = 0.35
                0.735m,         // Nível 2: 0.35 × 2.1^1 = 0.735
                1.5435m,        // Nível 3: 0.35 × 2.1^2 = 1.5435
                3.24135m,       // Nível 4: 0.35 × 2.1^3 = 3.24135
                6.806835m,      // Nível 5: 0.35 × 2.1^4 = 6.806835
                14.2943535m,    // Nível 6: 0.35 × 2.1^5 = 14.2943535
                30.01814235m,   // Nível 7: 0.35 × 2.1^6 = 30.01814235
                63.038298935m   // Nível 8: 0.35 × 2.1^7 = 63.038298935
            };

            // Valores incorretos que estavam sendo gerados (da imagem)
            decimal[] incorrectStakes = {
                0.35m,   // Nível 1: correto
                0.74m,   // Nível 2: INCORRETO (deveria ser 0.735)
                0.74m,   // Nível 3: MUITO INCORRETO (deveria ser 1.5435)
                1.54m,   // Nível 4: INCORRETO (deveria ser 3.24135)
                3.24m,   // Nível 5: INCORRETO (deveria ser 6.806835)
                14.29m   // Nível 6: INCORRETO (deveria ser 14.2943535)
            };

            Console.WriteLine("=== ANÁLISE DO PROBLEMA ===");
            Console.WriteLine("Valores INCORRETOS que estavam sendo gerados:");
            for (int i = 0; i < Math.Min(incorrectStakes.Length, expectedStakes.Length); i++)
            {
                var level = i + 1;
                var expected = expectedStakes[i];
                var incorrect = incorrectStakes[i];
                var difference = Math.Abs(expected - incorrect);

                Console.WriteLine($"Nível {level}:");
                Console.WriteLine($"  Esperado:  {expected:F8}");
                Console.WriteLine($"  Incorreto: {incorrect:F8}");
                Console.WriteLine($"  Diferença: {difference:F8}");

                if (difference > 0.00001m)
                {
                    Console.WriteLine($"  ❌ ERRO DETECTADO!");
                }
                else
                {
                    Console.WriteLine($"  ✅ Correto");
                }
                Console.WriteLine();
            }

            Console.WriteLine("=== TESTE DA FÓRMULA MATEMÁTICA SEQUENCIAL ===");
            Console.WriteLine("Testando a fórmula sequencial: stake = baseStake × factor × factor × ...");

            for (int level = 1; level <= 8; level++)
            {
                // Calcular usando multiplicação sequencial
                decimal calculatedStake = baseStake;
                for (int i = 1; i < level; i++)
                {
                    calculatedStake *= factor;
                }

                var expectedStake = expectedStakes[level - 1];
                var difference = Math.Abs(calculatedStake - expectedStake);

                Console.WriteLine($"Nível {level}:");
                Console.WriteLine($"  Fórmula sequencial: {baseStake} × {factor} (x{level-1}) = {calculatedStake:F8}");
                Console.WriteLine($"  Esperado: {expectedStake:F8}");
                Console.WriteLine($"  Diferença: {difference:F8}");

                if (difference > 0.00000001m)
                {
                    Console.WriteLine($"  ❌ ERRO na fórmula!");
                    return;
                }
                else
                {
                    Console.WriteLine($"  ✅ Fórmula correta");
                }
                Console.WriteLine();
            }

            Console.WriteLine("=== CAUSA DO PROBLEMA ===");
            Console.WriteLine("O problema estava em dois lugares:");
            Console.WriteLine("1. MartingaleService.cs linha 318: Math.Round(currentStake, 2) - REMOVIDO");
            Console.WriteLine("2. ProposalViewModel.cs linha 289: stake.ToString(\"F2\") - ALTERADO para F5");
            Console.WriteLine();
            Console.WriteLine("Essas correções preservam a precisão decimal necessária para");
            Console.WriteLine("a sequência de martingale funcionar corretamente.");

            Console.WriteLine("\n🎉 Análise da correção do martingale concluída!");
        }
    }
}
