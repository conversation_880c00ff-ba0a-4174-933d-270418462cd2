using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Excalibur.Models;

namespace Excalibur.Services;

/// <summary>
/// Optimized Martingale Service with correct mathematical implementation and improved performance
/// </summary>
public class MartingaleServiceOptimized : IDisposable
{
    private readonly ILogger<MartingaleServiceOptimized> _logger;
    private readonly ReaderWriterLockSlim _lock = new ReaderWriterLockSlim();
    
    // Core martingale configuration - decimal cannot be volatile, using locks instead
    private decimal _baseStake = 0.35m;
    private decimal _factor = 2.1m;
    private volatile int _maxLevel = 8;
    private volatile int _currentLevel = 1;
    
    // System state
    private volatile bool _hasActiveContract = false;
    private volatile string _activeContractId = string.Empty;
    
    // Performance optimizations
    private readonly ConcurrentDictionary<string, DateTime> _processedContracts = new ConcurrentDictionary<string, DateTime>();
    private readonly Timer _cleanupTimer;
    private const int CLEANUP_INTERVAL_MS = 300000; // 5 minutes
    private const int MAX_PROCESSED_CONTRACTS = 1000;
    
    // Events
    public event EventHandler<MartingaleResetEventArgs>? MartingaleReset;
    
    public MartingaleServiceOptimized(ILogger<MartingaleServiceOptimized> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Initialize cleanup timer
        _cleanupTimer = new Timer(CleanupProcessedContracts, null, CLEANUP_INTERVAL_MS, CLEANUP_INTERVAL_MS);
        
        _logger.LogInformation("[MARTINGALE] 🚀 Optimized Martingale Service initialized");
    }
    
    /// <summary>
    /// Initialize martingale settings with validation
    /// </summary>
    public void Initialize(decimal baseStake, decimal factor, int maxLevel)
    {
        if (baseStake <= 0) throw new ArgumentException("Base stake must be positive", nameof(baseStake));
        if (factor <= 1) throw new ArgumentException("Factor must be greater than 1", nameof(factor));
        if (maxLevel < 1) throw new ArgumentException("Max level must be at least 1", nameof(maxLevel));
        
        _lock.EnterWriteLock();
        try
        {
            _baseStake = baseStake;
            _factor = factor;
            _maxLevel = maxLevel;
            _currentLevel = 1;
            
            _logger.LogInformation("[MARTINGALE] ⚙️ Settings initialized - BaseStake: {BaseStake}, Factor: {Factor}, MaxLevel: {MaxLevel}", 
                baseStake, factor, maxLevel);
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }
    
    /// <summary>
    /// Calculate current stake using sequential multiplication: baseStake × factor × factor × ...
    /// </summary>
    public decimal CalculateCurrentStake()
    {
        _lock.EnterReadLock();
        try
        {
            // LÓGICA CORRIGIDA: Calcular stake sequencialmente
            // Nível 1 = baseStake
            // Nível 2 = baseStake × factor
            // Nível 3 = baseStake × factor × factor
            // etc.
            decimal stake = _baseStake;
            for (int i = 1; i < _currentLevel; i++)
            {
                stake *= _factor;
            }

            _logger.LogDebug("[MARTINGALE] 📊 Stake calculation - Level: {Level}, Sequential: {BaseStake} × {Factor} (x{Multiplications}) = {Stake}",
                _currentLevel, _baseStake, _factor, _currentLevel - 1, stake);

            return stake;
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }
    
    /// <summary>
    /// Process contract result with proper level management
    /// </summary>
    public void ProcessContractResult(bool isWin, string contractId)
    {
        if (string.IsNullOrEmpty(contractId))
        {
            _logger.LogWarning("[MARTINGALE] ⚠️ Contract ID is empty, skipping processing");
            return;
        }
        
        // Check if already processed
        if (_processedContracts.ContainsKey(contractId))
        {
            _logger.LogWarning("[MARTINGALE] ⚠️ Contract {ContractId} already processed", contractId);
            return;
        }
        
        _lock.EnterWriteLock();
        try
        {
            // Mark as processed
            _processedContracts.TryAdd(contractId, DateTime.UtcNow);
            
            // Clear active contract
            if (_activeContractId == contractId)
            {
                _hasActiveContract = false;
                _activeContractId = string.Empty;
            }
            
            if (isWin)
            {
                _logger.LogInformation("[MARTINGALE] ✅ WIN! Resetting to level 1. Contract: {ContractId}", contractId);
                
                // Reset to level 1 on win
                _currentLevel = 1;
                
                // Fire reset event
                MartingaleReset?.Invoke(this, new MartingaleResetEventArgs 
                { 
                    OriginalStake = _baseStake,
                    ResetReason = "Victory"
                });
            }
            else
            {
                _logger.LogInformation("[MARTINGALE] ❌ LOSS! Incrementing level. Contract: {ContractId}", contractId);
                
                // Increment level on loss
                if (_currentLevel < _maxLevel)
                {
                    _currentLevel++;
                    _logger.LogInformation("[MARTINGALE] 📈 Level increased to {Level}", _currentLevel);
                }
                else
                {
                    _logger.LogWarning("[MARTINGALE] 🔴 Max level {MaxLevel} reached, resetting to level 1", _maxLevel);
                    _currentLevel = 1;
                    
                    // Fire reset event
                    MartingaleReset?.Invoke(this, new MartingaleResetEventArgs 
                    { 
                        OriginalStake = _baseStake,
                        ResetReason = "MaxLevelReached"
                    });
                }
            }
            
            var nextStake = CalculateCurrentStake();
            _logger.LogInformation("[MARTINGALE] 🎯 Next entry stake: {NextStake} (Level: {Level})", nextStake, _currentLevel);
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }
    
    /// <summary>
    /// Register an active contract
    /// </summary>
    public void RegisterActiveContract(string contractId)
    {
        if (string.IsNullOrEmpty(contractId))
        {
            _logger.LogWarning("[MARTINGALE] ⚠️ Cannot register empty contract ID");
            return;
        }
        
        _lock.EnterWriteLock();
        try
        {
            _hasActiveContract = true;
            _activeContractId = contractId;
            
            _logger.LogInformation("[MARTINGALE] ✅ Active contract registered: {ContractId}", contractId);
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }
    
    /// <summary>
    /// Get current system state
    /// </summary>
    public (int Level, decimal Stake, bool HasActiveContract, string ActiveContractId) GetState()
    {
        _lock.EnterReadLock();
        try
        {
            return (_currentLevel, CalculateCurrentStake(), _hasActiveContract, _activeContractId);
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }
    
    /// <summary>
    /// Check if system can accept new entries
    /// </summary>
    public bool CanAcceptNewEntry()
    {
        return !_hasActiveContract;
    }
    
    /// <summary>
    /// Cleanup old processed contracts to prevent memory leaks
    /// </summary>
    private void CleanupProcessedContracts(object? state)
    {
        try
        {
            if (_processedContracts.Count <= MAX_PROCESSED_CONTRACTS) return;
            
            var cutoffTime = DateTime.UtcNow.AddHours(-1); // Keep only last hour
            var toRemove = new List<string>();
            
            foreach (var kvp in _processedContracts)
            {
                if (kvp.Value < cutoffTime)
                {
                    toRemove.Add(kvp.Key);
                }
            }
            
            foreach (var key in toRemove)
            {
                _processedContracts.TryRemove(key, out _);
            }
            
            if (toRemove.Count > 0)
            {
                _logger.LogDebug("[MARTINGALE] 🧹 Cleaned up {Count} old processed contracts", toRemove.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[MARTINGALE] ❌ Error during cleanup");
        }
    }
    
    public void Dispose()
    {
        _cleanupTimer?.Dispose();
        _lock?.Dispose();
        _logger.LogInformation("[MARTINGALE] 🛑 Optimized Martingale Service disposed");
    }
}


