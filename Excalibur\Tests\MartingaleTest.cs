using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Excalibur.Services;

namespace Excalibur.Tests
{
    /// <summary>
    /// Teste simples para verificar se o sistema de martingale está funcionando corretamente
    /// </summary>
    public class MartingaleTest
    {
        private readonly MartingaleService _martingaleService;
        private readonly ILogger<MartingaleService> _logger;

        public MartingaleTest()
        {
            // Criar logger simples para console
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<MartingaleService>();
            
            // Criar instância do serviço
            _martingaleService = new MartingaleService(_logger);
        }

        public async Task RunTests()
        {
            Console.WriteLine("=== INICIANDO TESTES DO MARTINGALE ===");
            
            // Teste 1: Inicialização
            await TestInitialization();
            
            // Teste 2: Primeira entrada (sem martingale)
            await TestFirstEntry();
            
            // Teste 3: Perda e incremento do martingale
            await TestLossAndIncrement();
            
            // Teste 4: Vitória e reset do martingale
            await TestWinAndReset();
            
            // Teste 5: Múltiplas perdas seguidas
            await TestMultipleLosses();
            
            Console.WriteLine("=== TESTES CONCLUÍDOS ===");
        }

        private async Task TestInitialization()
        {
            Console.WriteLine("\n--- Teste 1: Inicialização ---");
            
            // Inicializar com valores de teste
            decimal baseStake = 1.00m;
            decimal factor = 2.0m;
            int maxLevel = 5;
            
            _martingaleService.InitializeMartingaleSettings(baseStake, factor, maxLevel);
            
            // Verificar estado inicial
            var level = _martingaleService.GetCurrentMartingaleLevel();
            var lossCount = _martingaleService.GetCurrentLossCount();
            var needsMartingale = _martingaleService.NeedsMartingale();
            
            Console.WriteLine($"Estado inicial - Level: {level}, LossCount: {lossCount}, NeedsMartingale: {needsMartingale}");
            
            // Verificações
            if (level != 1) Console.WriteLine("❌ ERRO: Nível inicial deveria ser 1");
            if (lossCount != 0) Console.WriteLine("❌ ERRO: Contador de perdas inicial deveria ser 0");
            if (needsMartingale) Console.WriteLine("❌ ERRO: NeedsMartingale inicial deveria ser false");
            
            if (level == 1 && lossCount == 0 && !needsMartingale)
                Console.WriteLine("✅ Inicialização OK");
        }

        private async Task TestFirstEntry()
        {
            Console.WriteLine("\n--- Teste 2: Primeira entrada ---");
            
            decimal baseStake = 1.00m;
            bool executionCalled = false;
            decimal receivedStake = 0;
            
            // Função mock para simular execução de compra
            Func<decimal, Task<string>> mockExecution = async (stake) =>
            {
                executionCalled = true;
                receivedStake = stake;
                Console.WriteLine($"Mock execution chamado com stake: {stake}");
                return "mock_contract_123";
            };
            
            // Executar primeira entrada
            bool success = await _martingaleService.TryExecuteEntryAsync(baseStake, mockExecution);
            
            Console.WriteLine($"Primeira entrada - Success: {success}, ReceivedStake: {receivedStake}");
            
            // Verificações
            if (!success) Console.WriteLine("❌ ERRO: Primeira entrada deveria ter sucesso");
            if (!executionCalled) Console.WriteLine("❌ ERRO: Função de execução deveria ter sido chamada");
            if (receivedStake != baseStake) Console.WriteLine($"❌ ERRO: Stake deveria ser {baseStake}, mas foi {receivedStake}");
            
            if (success && executionCalled && receivedStake == baseStake)
                Console.WriteLine("✅ Primeira entrada OK");
        }

        private async Task TestLossAndIncrement()
        {
            Console.WriteLine("\n--- Teste 3: Perda e incremento ---");
            
            // Simular perda
            _martingaleService.ProcessContractResult(false, "mock_contract_123");
            
            // Verificar estado após perda
            var level = _martingaleService.GetCurrentMartingaleLevel();
            var lossCount = _martingaleService.GetCurrentLossCount();
            var needsMartingale = _martingaleService.NeedsMartingale();
            
            Console.WriteLine($"Após perda - Level: {level}, LossCount: {lossCount}, NeedsMartingale: {needsMartingale}");
            
            // Verificações
            if (level != 2) Console.WriteLine($"❌ ERRO: Nível deveria ser 2, mas é {level}");
            if (lossCount != 1) Console.WriteLine($"❌ ERRO: Contador de perdas deveria ser 1, mas é {lossCount}");
            if (!needsMartingale) Console.WriteLine("❌ ERRO: NeedsMartingale deveria ser true");
            
            if (level == 2 && lossCount == 1 && needsMartingale)
                Console.WriteLine("✅ Perda e incremento OK");
        }

        private async Task TestWinAndReset()
        {
            Console.WriteLine("\n--- Teste 4: Vitória e reset ---");
            
            // Simular vitória
            _martingaleService.ProcessContractResult(true, "mock_contract_124");
            
            // Verificar estado após vitória
            var level = _martingaleService.GetCurrentMartingaleLevel();
            var lossCount = _martingaleService.GetCurrentLossCount();
            var needsMartingale = _martingaleService.NeedsMartingale();
            
            Console.WriteLine($"Após vitória - Level: {level}, LossCount: {lossCount}, NeedsMartingale: {needsMartingale}");
            
            // Verificações
            if (level != 1) Console.WriteLine($"❌ ERRO: Nível deveria ser resetado para 1, mas é {level}");
            if (lossCount != 0) Console.WriteLine($"❌ ERRO: Contador de perdas deveria ser resetado para 0, mas é {lossCount}");
            if (needsMartingale) Console.WriteLine("❌ ERRO: NeedsMartingale deveria ser false após vitória");
            
            if (level == 1 && lossCount == 0 && !needsMartingale)
                Console.WriteLine("✅ Vitória e reset OK");
        }

        private async Task TestMultipleLosses()
        {
            Console.WriteLine("\n--- Teste 5: Múltiplas perdas (Sequência Martingale) ---");

            decimal baseStake = 0.35m;
            decimal factor = 2.1m;

            // IMPORTANTE: Inicializar o sistema antes de testar
            _martingaleService.InitializeMartingaleSettings(baseStake, factor, 5);

            Console.WriteLine($"Base Stake: {baseStake}, Factor: {factor}");
            Console.WriteLine("Sequência esperada (CORRIGIDA):");
            Console.WriteLine("Entrada 1 (nível 1): 0.35 (stake base)");
            Console.WriteLine("Após perda → Entrada 2 (nível 2): 0.735 (0.35 × 2.1^1)");
            Console.WriteLine("Após perda → Entrada 3 (nível 3): 1.5435 (0.35 × 2.1^2)");
            Console.WriteLine("Após perda → Entrada 4 (nível 4): 3.24135 (0.35 × 2.1^3)");
            Console.WriteLine("Após perda → Entrada 5 (nível 5): 6.806835 (0.35 × 2.1^4)");
            Console.WriteLine();

            // Primeira entrada (nível 1) - stake base
            var initialLevel = _martingaleService.GetCurrentMartingaleLevel();
            var initialStake = _martingaleService.GetCurrentStakeForNewEntry();
            Console.WriteLine($"Estado inicial - Level: {initialLevel}, Stake: {initialStake}");

            // Simular perdas seguidas e verificar a progressão
            for (int i = 1; i <= 4; i++)
            {
                Console.WriteLine($"\n--- Processando perda #{i} ---");

                // Estado antes da perda
                var levelBefore = _martingaleService.GetCurrentMartingaleLevel();
                Console.WriteLine($"Antes da perda - Level: {levelBefore}");

                // Processar perda
                _martingaleService.ProcessContractResult(false, $"mock_contract_loss_{i}");

                // Estado após a perda
                var levelAfter = _martingaleService.GetCurrentMartingaleLevel();
                var stakeAfter = _martingaleService.GetCurrentStakeForNewEntry();

                // Calcular stake esperado para o próximo nível usando lógica sequencial
                decimal expectedStake = baseStake;
                for (int j = 1; j < levelAfter; j++)
                {
                    expectedStake *= factor;
                }

                Console.WriteLine($"Após perda #{i}:");
                Console.WriteLine($"  Level: {levelAfter}");
                Console.WriteLine($"  Stake calculado: {stakeAfter:F8}");
                Console.WriteLine($"  Stake esperado: {expectedStake:F8}");
                Console.WriteLine($"  Diferença: {Math.Abs(stakeAfter - expectedStake):F8}");

                // CORREÇÃO: Usar tolerância muito menor para verificar precisão
                if (Math.Abs(stakeAfter - expectedStake) > 0.00000001m)
                {
                    Console.WriteLine($"❌ ERRO: Stake incorreto! Esperado: {expectedStake:F8}, Obtido: {stakeAfter:F8}");
                }
                else
                {
                    Console.WriteLine($"✅ Stake correto para nível {levelAfter}");
                }
            }

            // Simular vitória para reset
            Console.WriteLine("\n--- Testando reset após vitória ---");
            _martingaleService.ProcessContractResult(true, "mock_contract_win");
            var finalLevel = _martingaleService.GetCurrentMartingaleLevel();
            var finalStake = _martingaleService.GetCurrentStakeForNewEntry();

            Console.WriteLine($"Após vitória - Level: {finalLevel}, Stake: {finalStake}");

            if (finalLevel != 1 || finalStake != baseStake)
                Console.WriteLine($"❌ ERRO: Após vitória, deveria ser Level=1 e Stake={baseStake}, mas é Level={finalLevel} e Stake={finalStake}");
            else
                Console.WriteLine("✅ Reset após vitória funcionou corretamente");
        }
    }
}
